using UnityEngine;

public class DragItemHandler : MonoBehaviour
{
    private void OnEnable()
    {
        MouseInputHandler.OnMouseEnter += HandleMouseEnter;
        MouseInputHandler.OnMouseLeave += HandleMouseLeave;
        MouseInputHandler.OnMouseClick += HandleMouseClick;
    }

    private void OnDisable()
    {
        MouseInputHandler.OnMouseEnter -= HandleMouseEnter;
        MouseInputHandler.OnMouseLeave -= HandleMouseLeave;
        MouseInputHandler.OnMouseClick -= HandleMouseClick;
    }

    private void HandleMouseEnter(GameObject hitObject)
    {
        Debug.Log($"Mouse entered: {hitObject.name}");
    }

    private void HandleMouseLeave(GameObject hitObject)
    {
        Debug.Log($"Mouse left: {hitObject.name}");
    }

    private void HandleMouseClick(GameObject hitObject)
    {
        Debug.Log($"Mouse clicked: {hitObject.name}");
    }
}