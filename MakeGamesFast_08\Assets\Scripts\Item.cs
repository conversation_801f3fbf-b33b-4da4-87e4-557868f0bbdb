using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(Image))]
public class Item : MonoBehaviour
{
    public string Name;
    public Vector2 Size;

    public Item(string Name, Vector2 Size)
    {
        this.Name = Name;
        this.Size = Size;
    }

    private void Awake()
    {
        UpdateSize();
    }

    private void UpdateSize()
    {
        RectTransform rectTransform = this.transform as RectTransform;
        rectTransform.sizeDelta = new Vector2(InventoryStats.CELL_SIZE * Size.x, InventoryStats.CELL_SIZE * Size.y);
        // make the anchor the top left without changing the position
        //rectTransform.anchorMin = new Vector2(0, 1);
        //rectTransform.anchorMax = new Vector2(0, 1);
    }

    public class Builder
    {
        public string Name;
        public Vector2 Size;

        public Builder(string name, Vector2 size) //Sprite icon, 
        {
            Name = name;
            //Icon = icon;
            Size = size;
        }

        public Item Build()
        {
            GameObject itemObject = new GameObject("Item_" + Name, typeof(RectTransform));
            Item item = itemObject.AddComponent<Item>();
            item.Name = Name;
            //item.Icon = Icon;
            item.Size = Size;

            item.UpdateSize();

            return item;
        }
    }
}