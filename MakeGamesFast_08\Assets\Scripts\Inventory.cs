using UnityEngine;
using UnityEngine.UI;
using Sirenix.OdinInspector;

[RequireComponent(typeof(VariableGridLayout), typeof(RectTransform))]
public class Inventory : MonoBehaviour
{
    public Vector2 InventorySize = new Vector2(4, 4);

    public InventorySlot[,] Slots;

    void Awake()
    {
        VariableGridLayout variableGridLayout = GetComponent<VariableGridLayout>();
        variableGridLayout.spacingX = InventoryStats.CELL_GAP;
        variableGridLayout.spacingY = InventoryStats.CELL_GAP;

        InitializeInventorySlots();
    }

    public void InitializeInventorySlots()
    {
        // TODO :Add logic that removes all items from the inventory before initializing it again

        // Initialize the 2D array based on InventorySize
        Slots = new InventorySlot[(int)InventorySize.x, (int)InventorySize.y];

        // Populate the array with empty slots
        for (int x = 0; x < InventorySize.x; x++)
        {
            for (int y = 0; y < InventorySize.y; y++)
            {
                Slots[x, y] = new InventorySlot.Builder(x, y).WithParent(transform).Build();
            }
        }

        UpdateInventorySize();
    }


    [Button]
    public void UpdateInventorySize()
    {
        // Set the size of the inventory based on the number of slots
        RectTransform rectTransform = this.transform as RectTransform;
        VariableGridLayout variableGridLayout = GetComponent<VariableGridLayout>();
        float width = InventorySize.x * InventoryStats.CELL_SIZE - InventoryStats.CELL_GAP + variableGridLayout.padding.left + +variableGridLayout.padding.right;
        float height = InventorySize.y * InventoryStats.CELL_SIZE - InventoryStats.CELL_GAP + variableGridLayout.padding.top + +variableGridLayout.padding.bottom;
        rectTransform.sizeDelta = new Vector2(width, height);
    }


}
