using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(Image))]
public class InventorySlot : MonoBehaviour
{
    public int X;
    public int Y;
    public Item Item = null;
    private bool isInitialized = false;

    private void Awake()
    {
        Initialize();
    }

    public void Initialize()
    {
        if (isInitialized) return;

        UpdateSize();

        isInitialized = true;
    }

    private void UpdateSize()
    {
        RectTransform rectTransform = this.transform as RectTransform;
        rectTransform.sizeDelta = new Vector2(InventoryStats.CELL_SIZE - InventoryStats.CELL_GAP, InventoryStats.CELL_SIZE - InventoryStats.CELL_GAP);
        // make the anchor the top left without changing the position
        rectTransform.anchorMin = new Vector2(0, 1);
        rectTransform.anchorMax = new Vector2(0, 1);
    }

    public class Builder
    {
        public int X;
        public int Y;
        public Transform Parent = null;

        public Builder(int x, int y)
        {
            X = x;
            Y = y;
        }
        public Builder WithParent(Transform parent)
        {
            Parent = parent;
            return this;
        }


        public InventorySlot Build()
        {
            GameObject inventorySlotObject = new GameObject("InventorySlot_" + X + "_" + Y, typeof(RectTransform));
            InventorySlot inventorySlot = inventorySlotObject.AddComponent<InventorySlot>();
            inventorySlot.X = X;
            inventorySlot.Y = Y;
            inventorySlot.isInitialized = true;
            inventorySlot.UpdateSize();

            RectTransform rectTransform = inventorySlotObject.transform as RectTransform;
            rectTransform.SetParent(Parent);

            return inventorySlot;
        }
    }
}
